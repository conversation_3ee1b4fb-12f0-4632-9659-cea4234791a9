import type { ApiResponse } from '~/types/api/common'
import { ErrorCode } from '~/utils/error-code'

/**
 * Credits不足错误类
 */
export class InsufficientBalanceError extends Error {
  constructor(message: string = 'Insufficient balance') {
    super(message)
    this.name = 'InsufficientBalanceError'
  }
}

export const getErrorMessage = (response: ApiResponse): string => {
  if (!response.error) return ''

  const errorCode = Object.values(ErrorCode).find(code => code.code === response.errorCode)
  return response.errorMessage || errorCode?.displayMessage || '操作失败，请稍后重试'
}

export const handleApiError = (error: any): string => {
  if (error.data instanceof Object) {
    return getErrorMessage(error.data)
  }
  return error.message || '操作失败，请稍后重试'
}
