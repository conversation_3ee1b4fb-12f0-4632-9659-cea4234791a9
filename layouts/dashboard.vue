<template>
  <div class="scrollbar">
    <!-- 左侧悬停感应区域 - 当侧边栏收起时显示 -->
    <div v-if="isSidebarCollapsed && !isHovering" class="fixed left-0 top-0 z-20 h-full w-[250px] bg-transparent" style="top: 56px"></div>

    <!-- 全局导航栏 -->
    <GlobalNavbar>
      <template #sidebar-toggle>
        <Bars3Icon class="m-5 size-14 shrink-0 p-2.5 text-gray-600 text-gray-700" @click="toggleSidebar" />
      </template>
    </GlobalNavbar>

    <TransitionRoot as="template" :show="sidebarOpen">
      <Dialog class="relative z-50 lg:hidden" @close="sidebarOpen = false">
        <TransitionChild
          as="template"
          enter="transition-opacity ease-linear duration-300"
          enter-from="opacity-0"
          enter-to="opacity-100"
          leave="transition-opacity ease-linear duration-300"
          leave-from="opacity-100"
          leave-to="opacity-0"
        >
          <div class="fixed inset-0 bg-gray-900/80" />
        </TransitionChild>

        <div class="fixed inset-0 flex">
          <TransitionChild
            as="template"
            enter="transition ease-in-out duration-300 transform"
            enter-from="-translate-x-full"
            enter-to="translate-x-0"
            leave="transition ease-in-out duration-300 transform"
            leave-from="translate-x-0"
            leave-to="-translate-x-full"
          >
            <DialogPanel class="relative mr-16 flex w-full max-w-xs flex-1">
              <TransitionChild
                as="template"
                enter="ease-in-out duration-300"
                enter-from="opacity-0"
                enter-to="opacity-100"
                leave="ease-in-out duration-300"
                leave-from="opacity-100"
                leave-to="opacity-0"
              >
                <div class="absolute left-full top-0 flex w-16 justify-center pt-5">
                  <button type="button" class="-m-2.5 p-2.5" @click="sidebarOpen = false">
                    <span class="sr-only">关闭侧边栏</span>
                    <XMarkIcon class="size-6 text-white" aria-hidden="true" />
                  </button>
                </div>
              </TransitionChild>
              <!-- Sidebar component, swap this element with another sidebar if you like -->
              <div class="flex grow flex-col gap-y-6 overflow-y-auto bg-white px-4 py-6 shadow-lg">
                <div class="flex h-12 shrink-0 items-center">
                  <Logo class="h-8 w-auto" />
                  <span class="ml-2 text-xl font-semibold text-gray-900">Hi-Offer</span>
                </div>

                <div class="mt-2">
                  <div class="mb-4 text-sm font-medium text-gray-500">功能</div>

                  <ul role="list" class="space-y-3">
                    <li v-for="item in navigation" :key="item.name">
                      <NuxtLink
                        :to="item.href"
                        :class="[
                          item.current
                            ? 'border-l-4 border-indigo-600 bg-indigo-50 text-indigo-700 shadow-sm'
                            : 'text-gray-700 hover:bg-gray-50 hover:text-indigo-600',
                          'group flex items-center gap-x-3 rounded-md p-3 text-sm font-medium transition-all duration-200 ease-in-out',
                        ]"
                        @click="sidebarOpen = false"
                      >
                        <component
                          :is="item.icon"
                          :class="[
                            item.current ? 'text-indigo-700' : 'text-gray-400 group-hover:text-indigo-600',
                            'size-5 shrink-0 transition-colors duration-200',
                          ]"
                          aria-hidden="true"
                        />
                        {{ item.name }}
                      </NuxtLink>
                    </li>
                  </ul>
                </div>

                <div class="mt-6">
                  <div class="mb-4 text-sm font-medium text-gray-500">工具</div>

                  <ul role="list" class="space-y-3">
                    <li>
                      <NuxtLink
                        to="/settings"
                        :class="[
                          route.path === '/settings'
                            ? 'border-l-4 border-indigo-600 bg-indigo-50 text-indigo-700 shadow-sm'
                            : 'text-gray-700 hover:bg-gray-50/80 hover:text-indigo-600',
                          'group flex items-center gap-x-3 rounded-md p-3 text-sm font-medium transition-all duration-200 ease-in-out',
                        ]"
                      >
                        <Cog6ToothIcon
                          :class="[
                            route.path === '/settings' ? 'text-indigo-700' : 'text-gray-400 group-hover:text-indigo-600',
                            'size-5 shrink-0 transition-colors duration-200',
                          ]"
                          aria-hidden="true"
                        />
                        设置
                      </NuxtLink>
                    </li>
                  </ul>
                </div>

                <!-- 信用额度卡片 (移动端) -->
                <div class="mt-3">
                  <div class="rounded-lg bg-purple-50 p-4">
                    <div class="mb-1 flex items-center gap-x-2">
                      <CurrencyDollarIcon class="size-5 text-purple-600" aria-hidden="true" />
                      <span class="text-base font-medium text-purple-600">Credits</span>
                    </div>
                    <div class="mb-3 flex items-center justify-between">
                      <p class="text-[14px] font-medium text-gray-600">
                        剩余:<span class="ml-3 text-xl font-bold text-purple-500">{{ remainingCredits }}</span>
                      </p>
                    </div>
                    <UiButton
                      @click="
                        () => {
                          showPaywallModal = true
                          sidebarOpen = false
                        }
                      "
                      variant="primary"
                      size="sm"
                      full-width
                    >
                      购买更多
                    </UiButton>
                  </div>
                </div>

                <!-- 用户信息 (移动端) -->
                <div class="mt-auto pt-6">
                  <div class="rounded-lg bg-gradient-to-br from-gray-50 to-gray-100 p-4 shadow-sm ring-1 ring-gray-200/50 backdrop-blur-sm">
                    <div class="flex items-center">
                      <div class="relative">
                        <img class="size-10 rounded-full object-cover ring-2 ring-white/90 ring-offset-1" :src="userInfo.image" :alt="userInfo.name" />
                        <div class="absolute -bottom-1 -right-1 size-3 rounded-full bg-green-400 ring-1 ring-white"></div>
                      </div>
                      <div class="ml-3 flex flex-col">
                        <p class="text-sm font-medium text-gray-800">{{ userInfo.name }}</p>
                        <UiButton @click="handleLogout" variant="ghost" size="xs" class="!mt-0.5 !justify-start !px-0 !py-0 !text-xs"> 退出登录 </UiButton>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </DialogPanel>
          </TransitionChild>
        </div>
      </Dialog>
    </TransitionRoot>

    <!-- 桌面端侧边栏 -->
    <div
      class="fixed inset-y-0 z-30 flex flex-col overflow-hidden p-4 transition-all ease-out"
      :class="{ hidden: screenWidth < 1024 }"
      style="transition-duration: 300ms; top: 56px"
      :style="{
        width: isSidebarCollapsed && !isHovering ? '0' : '18rem',
        opacity: isSidebarCollapsed && !isHovering ? '0' : '1',
        transform: isSidebarCollapsed && !isHovering ? 'translateX(-100%)' : 'translateX(0)',
        overflow: isSidebarCollapsed && !isHovering ? 'hidden' : 'visible',
      }"
    >
      <!-- 侧边栏内容 -->
      <div
        class="flex grow flex-col gap-y-6 overflow-y-auto rounded-xl bg-white px-4 py-6 shadow-xl ring-1 ring-gray-200/60 transition-all duration-150"
        :class="{ 'pointer-events-none scale-95 opacity-0': isSidebarCollapsed && !isHovering }"
        style="min-width: 0; transform-origin: left center"
      >
        <div class="flex h-12 shrink-0 items-center">
          <!-- <Logo class="h-8 w-auto" /> -->
          <span class="ml-2 bg-gradient-to-r from-purple-400 to-indigo-600 bg-clip-text text-xl font-semibold text-transparent">Hi-Offer</span>
        </div>

        <div class="mt-2">
          <div class="mb-4 text-sm font-medium text-gray-500">功能</div>

          <ul role="list" class="space-y-3">
            <li v-for="item in navigation" :key="item.name">
              <NuxtLink
                :to="item.href"
                :class="[
                  item.current
                    ? 'border-l-4 border-indigo-600 bg-indigo-50 text-indigo-700 shadow-sm'
                    : 'text-gray-700 hover:bg-gray-50/80 hover:text-indigo-600',
                  'group flex items-center gap-x-3 rounded-md p-3 text-sm font-medium transition-all duration-200 ease-in-out',
                ]"
              >
                <component
                  :is="item.icon"
                  :class="[item.current ? 'text-indigo-700' : 'text-gray-400 group-hover:text-indigo-600', 'size-5 shrink-0 transition-colors duration-200']"
                  aria-hidden="true"
                />
                {{ item.name }}
              </NuxtLink>
            </li>
          </ul>
        </div>

        <div class="mt-6">
          <div class="mb-4 text-sm font-medium text-gray-500">工具</div>

          <ul role="list" class="space-y-3">
            <li>
              <NuxtLink
                to="/settings"
                :class="[
                  route.path === '/settings'
                    ? 'border-l-4 border-indigo-600 bg-indigo-50 text-indigo-700 shadow-sm'
                    : 'text-gray-700 hover:bg-gray-50/80 hover:text-indigo-600',
                  'group flex items-center gap-x-3 rounded-md p-3 text-sm font-medium transition-all duration-200 ease-in-out',
                ]"
              >
                <Cog6ToothIcon
                  :class="[
                    route.path === '/settings' ? 'text-indigo-700' : 'text-gray-400 group-hover:text-indigo-600',
                    'size-5 shrink-0 transition-colors duration-200',
                  ]"
                  aria-hidden="true"
                />
                设置
              </NuxtLink>
            </li>
          </ul>
        </div>

        <!-- 信用额度卡片 -->
        <div class="mt-3">
          <div class="rounded-lg bg-purple-50 p-4">
            <div class="mb-1 flex items-center gap-x-2">
              <CurrencyDollarIcon class="size-5 text-purple-600" aria-hidden="true" />
              <span class="text-base font-medium text-purple-600">Credits</span>
            </div>
            <div class="mb-3 flex items-center justify-between">
              <p class="text-[14px] font-medium text-gray-600">
                剩余:<span class="ml-3 text-xl font-bold text-purple-500">{{ remainingCredits }}</span>
              </p>
            </div>
            <UiButton @click="showPaywallModal = true" variant="primary" size="sm" full-width> 购买更多 </UiButton>
          </div>
        </div>

        <!-- <div class="mt-auto pt-6">
          <div class="rounded-lg bg-gradient-to-br from-gray-50 to-gray-100 p-4 shadow-sm ring-1 ring-gray-200/50 backdrop-blur-sm">
            <div class="flex items-center">
              <div class="relative">
                <img class="size-10 rounded-full object-cover ring-2 ring-white/90 ring-offset-1" :src="userInfo.image" :alt="userInfo.name" />
                <div class="absolute -bottom-1 -right-1 size-3 rounded-full bg-green-400 ring-1 ring-white"></div>
              </div>
              <div class="ml-3 flex flex-col">
                <p class="text-sm font-medium text-gray-800">{{ userInfo.name }}</p>
                <UiButton @click="handleLogout" variant="ghost" size="xs" class="!mt-0.5 !justify-start !px-0 !py-0 !text-xs"> 退出登录 </UiButton>
              </div>
            </div>
          </div>
        </div> -->
      </div>
    </div>

    <!-- 主内容区域 -->
    <div
      class="transition-all ease-out"
      style="transition-duration: 300ms"
      :style="{
        paddingLeft: screenWidth >= 1024 ? (isSidebarCollapsed && !isHovering ? '0' : '18rem') : '0',
      }"
    >
      <!-- Mobile header with hamburger menu -->
      <!-- <div class="sticky top-0 z-40 flex h-16 shrink-0 items-center gap-x-4 border-b border-gray-200 bg-white px-4 shadow-sm sm:gap-x-6 sm:px-6 lg:hidden">
        <button type="button" class="-m-2.5 p-2.5 text-gray-700" @click="sidebarOpen = true">
          <span class="sr-only">打开侧边栏</span>
          <Bars3Icon class="size-6" aria-hidden="true" />
        </button>

     
        <div class="h-6 w-px bg-gray-200" aria-hidden="true" />

        <div class="flex flex-1 items-center gap-x-4 self-stretch">
          <div class="flex items-center gap-x-2">
            <Logo class="h-7 w-auto" />
            <span class="bg-gradient-to-r from-purple-400 to-indigo-600 bg-clip-text text-lg font-semibold text-transparent">Hi-Offer</span>
          </div>

          <div class="ml-auto flex items-center gap-x-1 text-sm">
            <CurrencyDollarIcon class="size-4 text-purple-600" aria-hidden="true" />
            <span class="font-medium text-purple-600">{{ remainingCredits }}</span>
          </div>
        </div>
      </div> -->

      <main>
        <div class="">
          <slot />
        </div>
      </main>
    </div>
  </div>

  <!-- Paywall Modal -->
  <PaywallModal :isOpen="showPaywallModal" @close="showPaywallModal = false" />

  <!-- Credits 不足通知 -->
  <CreditsNotification :isVisible="showCreditsNotification" @close="handleCreditsNotificationClose" @purchase="handleCreditsNotificationPurchase" />
</template>

<script setup>
import { Dialog, DialogPanel, TransitionChild, TransitionRoot } from '@headlessui/vue'
import { Cog6ToothIcon, HomeIcon, XMarkIcon, DocumentTextIcon, CurrencyDollarIcon, BriefcaseIcon, Bars3Icon } from '@heroicons/vue/24/outline'
import Logo from '@/assets/icons/logo.svg'
import { useEventBus, useEvent } from '@/composables/useEventBus'
import { useRoute } from 'vue-router'
import { isEqual } from 'lodash-es'

const { signOut, data: session } = useAuth()
const userStore = useUserStore()
const { remainingCredits } = useCredit()

// 响应式屏幕宽度
const screenWidth = ref(0)

// 侧边栏状态控制
const sidebarOpen = ref(false)
const isSidebarCollapsed = ref(false)
const isHovering = ref(false) // 控制鼠标悬停状态

// 更新屏幕宽度
const updateScreenWidth = () => {
  screenWidth.value = window.innerWidth
}

// 切换侧边栏
const toggleSidebar = () => {
  if (screenWidth.value >= 1024) {
    // 桌面端 - 收起/展开
    isSidebarCollapsed.value = !isSidebarCollapsed.value
  } else {
    // 移动端 - 显示/隐藏对话框
    sidebarOpen.value = !sidebarOpen.value
  }
}

// 处理鼠标移动事件
const handleMouseMove = event => {
  if (isSidebarCollapsed.value) {
    // 获取鼠标位置
    const mouseX = event.clientX
    const mouseY = event.clientY

    // 导航栏高度(包括一点余量)
    const navbarHeight = 60

    // 只有当鼠标在左侧150px区域内且不在导航栏区域时才显示侧边栏
    isHovering.value = mouseX <= 250 && mouseY > navbarHeight
  }
}

// 在组件挂载时添加鼠标移动事件监听
onMounted(() => {
  updateScreenWidth()
  document.addEventListener('mousemove', handleMouseMove)
  window.addEventListener('resize', updateScreenWidth)
})

// 在组件卸载时移除鼠标移动事件监听
onUnmounted(() => {
  document.removeEventListener('mousemove', handleMouseMove)
  window.removeEventListener('resize', updateScreenWidth)
})
const eventBus = useEventBus()
const { on: onShowPaywall } = useEvent('showPaywall')
const { on: onShowCreditsNotification } = useEvent('showCreditsNotification')

watch(
  () => session.value?.user,
  (newUser, oldUser) => {
    if (newUser && (!oldUser || !isEqual(newUser, oldUser))) {
      userStore.fetchProfile()
    }
  },
  { immediate: true },
)

const userInfo = computed(() => ({
  name: userStore.name || '未设置姓名',
  image: userStore.image,
}))

const handleLogout = async () => {
  try {
    await signOut({ callbackUrl: '/' })
  } catch (error) {
    console.error('退出登录失败:', error)
    eventBus.emit('showToast', { message: '退出登录失败', type: 'error' })
  }
}

const route = useRoute()

const pageTitle = computed(() => {
  if (route.path.startsWith('/interviews/')) {
    return '面试详情'
  }
  if (route.path === '/interviews') {
    return '面试记录'
  }
  return '工作台'
})

useHead({
  title: computed(() => `${pageTitle.value} - Hi-Offer`),
})

const navigation = computed(() => [
  // { name: '面试记录', href: '/interviews', icon: DocumentDuplicateIcon, current: route.path.startsWith('/interviews') },
  { name: '简历服务', href: '/dashboard/resume', icon: DocumentTextIcon, current: route.path === '/dashboard/resume' },
  { name: '模拟面试', href: '/dashboard', icon: HomeIcon, current: route.path === '/dashboard' },
  { name: 'Offer助手', href: '/dashboard/offer', icon: BriefcaseIcon, current: route.path === '/dashboard/offer' },
])

const showPaywallModal = ref(false)
const showCreditsNotification = ref(false)

onShowPaywall(() => {
  showPaywallModal.value = true
})

// 监听显示credits不足通知事件
onShowCreditsNotification(() => {
  showCreditsNotification.value = true
})

// 处理credits通知的购买按钮点击
const handleCreditsNotificationPurchase = () => {
  showPaywallModal.value = true
}

// 处理credits通知关闭
const handleCreditsNotificationClose = () => {
  showCreditsNotification.value = false
}
</script>
