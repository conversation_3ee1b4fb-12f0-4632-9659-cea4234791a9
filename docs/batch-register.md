# 批量注册内测用户接口

## 概述

这个接口允许管理员批量注册内测用户，每个用户将获得100个免费credit（100,000 tokens）。

## 功能特性

- ✅ 批量注册多个内测用户
- ✅ 自动生成随机密码
- ✅ 每个用户获得100 credit（100,000 tokens）
- ✅ 自动创建用户钱包
- ✅ 邮箱去重处理
- ✅ 详细的注册结果反馈
- ✅ 管理员密钥验证
- ✅ Web界面支持

## API接口

### 端点
```
POST /api/admin/batch-register
```

### 请求参数
```json
{
  "emails": ["<EMAIL>", "<EMAIL>"],
  "adminKey": "your-admin-key"
}
```

### 响应格式
```json
{
  "success": true,
  "data": {
    "message": "批量注册完成，成功: 2，失败: 0",
    "results": [
      {
        "email": "<EMAIL>",
        "password": "Abc123XyZ9",
        "success": true,
        "userId": "uuid-string"
      },
      {
        "email": "<EMAIL>", 
        "password": "Def456UvW8",
        "success": true,
        "userId": "uuid-string"
      }
    ],
    "summary": {
      "total": 2,
      "success": 2,
      "failure": 0
    }
  }
}
```

## 环境配置

在 `.env` 文件中添加管理员密钥：

```bash
ADMIN_BATCH_REGISTER_KEY=your-secure-admin-key-here
```

## Web界面使用

1. 访问 `/admin/batch-register` 页面
2. 输入管理员密钥
3. 在文本框中输入邮箱列表（每行一个）
4. 点击"批量注册用户"按钮
5. 查看注册结果
6. 可以导出成功注册的用户信息为CSV文件

## 使用限制

- 一次最多注册100个用户
- 需要有效的管理员密钥
- 邮箱格式必须正确
- 重复邮箱会被自动去重
- 已存在的用户会跳过注册

## 用户默认配置

每个注册的内测用户将获得：

- **免费额度**: 100 credit (100,000 tokens)
- **随机密码**: 10位字符（包含大小写字母和数字）
- **随机用户名**: 基于邮箱生成
- **随机昵称**: 使用形容词+动物的组合
- **随机头像**: 使用 DiceBear API 生成

## 安全注意事项

1. **管理员密钥**: 请使用强密码作为管理员密钥
2. **HTTPS**: 生产环境请确保使用HTTPS
3. **访问控制**: 建议在生产环境中添加IP白名单
4. **日志记录**: 所有批量注册操作都会被记录到日志中
5. **密码安全**: 生成的密码请及时通知用户并建议修改

## 错误处理

常见错误及解决方案：

- `管理员密钥错误`: 检查环境变量配置
- `邮箱格式无效`: 确保邮箱格式正确
- `用户已存在`: 该邮箱已注册，会跳过
- `一次最多注册100个用户`: 减少邮箱数量

## 示例代码

### 使用 curl 调用接口

```bash
curl -X POST http://localhost:3000/api/admin/batch-register \
  -H "Content-Type: application/json" \
  -d '{
    "emails": ["<EMAIL>", "<EMAIL>"],
    "adminKey": "your-admin-key"
  }'
```

### 使用 JavaScript 调用

```javascript
const response = await fetch('/api/admin/batch-register', {
  method: 'POST',
  headers: {
    'Content-Type': 'application/json',
  },
  body: JSON.stringify({
    emails: ['<EMAIL>', '<EMAIL>'],
    adminKey: 'your-admin-key'
  })
});

const result = await response.json();
console.log(result);
```

## 日志记录

系统会记录以下操作日志：

- 批量注册开始和完成
- 每个用户的创建成功/失败
- 管理员密钥验证失败尝试
- 错误详情和堆栈信息

## 后续建议

1. **通知用户**: 建议通过邮件通知用户账号和密码
2. **密码修改**: 建议用户首次登录后修改密码
3. **使用指导**: 为内测用户提供平台使用指导
4. **反馈收集**: 建立内测用户反馈收集机制
