FROM node:22-slim AS base
# cert install && openssl install
RUN apt-get update && apt-get install -y --no-install-recommends \
    ca-certificates \
    && rm -rf /var/lib/apt/lists/*


# 构建阶段
FROM base AS builder

# 安装 pnpm
RUN corepack enable && corepack prepare pnpm@10.14.0 --activate

# 设置工作目录
WORKDIR /app

# 复制 package.json 和 pnpm-lock.yaml
COPY package.json pnpm-lock.yaml ./

# 安装依赖
RUN pnpm install --frozen-lockfile

# 复制源代码
COPY . .

# 构建应用
RUN pnpm run build

# 生产阶段
FROM base AS production

# 安装 pnpm
RUN corepack enable && corepack prepare pnpm@10.14.0 --activate

# 设置工作目录
WORKDIR /app

# 复制构建产物和必要文件
COPY --from=builder /app/.output ./

# 设置环境变量
ENV NODE_ENV=production

# 暴露端口
EXPOSE 3131

# 启动命令
CMD ["node", "/app/server/index.mjs"]