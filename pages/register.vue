<template>
  <div class="flex min-h-full flex-1 flex-col justify-center py-12 sm:px-6 lg:px-8">
    <div class="sm:mx-auto sm:w-full sm:max-w-md">
      <NuxtLink to="/">
        <Logo class="mx-auto h-10 w-auto" />
      </NuxtLink>
      <h2 class="mt-6 text-center text-2xl/9 font-bold tracking-tight text-gray-900">创建新账号</h2>
    </div>

    <div class="mt-10 sm:mx-auto sm:w-full sm:max-w-[480px]">
      <div class="bg-white px-6 py-12 shadow sm:rounded-lg sm:px-12">
        <div class="space-y-6">
          <div>
            <label for="email" class="block text-sm/6 font-medium text-gray-900">邮箱</label>
            <div class="mt-2">
              <input
                v-model="form.email"
                type="email"
                id="email"
                autocomplete="email"
                class="block w-full rounded-md bg-white px-3 py-1.5 text-base text-gray-900 outline outline-1 -outline-offset-1 outline-gray-300 placeholder:text-gray-400 focus:outline focus:outline-2 focus:-outline-offset-2 focus:outline-indigo-600 sm:text-sm/6"
              />
            </div>
          </div>

          <div>
            <label for="code" class="block text-sm/6 font-medium text-gray-900">验证码</label>
            <div class="mt-2 flex gap-2">
              <input
                v-model="form.code"
                type="text"
                id="code"
                maxlength="6"
                placeholder="请输入6位验证码"
                class="block w-full rounded-md bg-white px-3 py-1.5 text-base text-gray-900 outline outline-1 -outline-offset-1 outline-gray-300 placeholder:text-gray-400 focus:outline focus:outline-2 focus:-outline-offset-2 focus:outline-indigo-600 sm:text-sm/6"
              />
              <button
                @click="sendVerificationCode"
                :disabled="isCodeSending || countdown > 0"
                type="button"
                class="shrink-0 rounded-md bg-indigo-600 px-3 py-1.5 text-sm/6 font-semibold text-white shadow-sm hover:bg-indigo-500 focus-visible:outline focus-visible:outline-2 focus-visible:outline-offset-2 focus-visible:outline-indigo-600 disabled:cursor-not-allowed disabled:opacity-50"
              >
                {{ countdown > 0 ? `${countdown}秒后重试` : '获取验证码' }}
              </button>
            </div>
          </div>

          <div>
            <label for="password" class="block text-sm/6 font-medium text-gray-900">密码</label>
            <div class="mt-2">
              <input
                v-model="form.password"
                type="password"
                id="password"
                autocomplete="new-password"
                class="block w-full rounded-md bg-white px-3 py-1.5 text-base text-gray-900 outline outline-1 -outline-offset-1 outline-gray-300 placeholder:text-gray-400 focus:outline focus:outline-2 focus:-outline-offset-2 focus:outline-indigo-600 sm:text-sm/6"
              />
            </div>
          </div>

          <div>
            <label for="confirm-password" class="block text-sm/6 font-medium text-gray-900">确认密码</label>
            <div class="mt-2">
              <input
                v-model="form.confirmPassword"
                type="password"
                id="confirm-password"
                autocomplete="new-password"
                class="block w-full rounded-md bg-white px-3 py-1.5 text-base text-gray-900 outline outline-1 -outline-offset-1 outline-gray-300 placeholder:text-gray-400 focus:outline focus:outline-2 focus:-outline-offset-2 focus:outline-indigo-600 sm:text-sm/6"
              />
            </div>
          </div>

          <div>
            <UiButton
              @click="handleSubmit"
              :disabled="isLoading"
              :loading="isLoading"
              variant="primary"
              size="md"
              full-width
            >
              {{ isLoading ? '注册中...' : '注册' }}
            </UiButton>
          </div>
        </div>

        <div class="mt-6 text-center text-sm/6 text-gray-500">
          已有账号？
          <NuxtLink to="/login" class="font-semibold text-indigo-600 hover:text-indigo-500">立即登录</NuxtLink>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import Logo from '@/assets/icons/logo.svg'
import { useEventBus } from '@/composables/useEventBus'
import type { ApiResponse } from '~/types/index'
import { handleApiError } from '~/utils/error'
import { z } from 'zod'
import { apiFetch } from '~/utils/api'

const form = ref({
  email: '',
  code: '',
  password: '',
  confirmPassword: '',
})

const isLoading = ref(false)
const isCodeSending = ref(false)
const countdown = ref(0)
const router = useRouter()
const eventBus = useEventBus()

// 邮箱验证
const emailSchema = z.object({
  email: z.string().min(1, '请输入邮箱').email('请输入有效的邮箱地址'),
})

// 验证码验证
const codeSchema = z.object({
  code: z.string().length(6, '验证码必须是6位数字'),
})

// 注册表单验证
const registerSchema = z
  .object({
    email: z.string().min(1, '请输入邮箱').email('请输入有效的邮箱地址'),
    code: z.string().min(1, '请输入验证码').length(6, '验证码必须是6位数字'),
    password: z.string().min(1, '请输入密码').min(6, '密码长度至少为6位'),
    confirmPassword: z.string().min(1, '请确认密码'),
  })
  .refine(data => data.password === data.confirmPassword, {
    message: '两次输入的密码不一致',
    path: ['confirmPassword'],
  })

// 发送验证码
const sendVerificationCode = async () => {
  try {
    const result = emailSchema.safeParse({ email: form.value.email })
    if (!result.success) {
      const firstError = result.error.errors[0]
      eventBus.emit('showToast', { message: firstError.message, type: 'warning' })
      return
    }

    isCodeSending.value = true
    await apiFetch('/api/sms/email', {
      method: 'POST',
      body: { email: form.value.email },
    })
    eventBus.emit('showToast', { message: '验证码已发送，请查收邮件', type: 'success' })
    startCountdown()
  } catch (error) {
    console.error(error)
    eventBus.emit('showToast', { message: '发送验证码失败，请稍后重试', type: 'error' })
  } finally {
    isCodeSending.value = false
  }
}

// 倒计时
const startCountdown = () => {
  countdown.value = 60
  const timer = setInterval(() => {
    countdown.value--
    if (countdown.value <= 0) {
      clearInterval(timer)
    }
  }, 1000)
}

const validateForm = () => {
  try {
    registerSchema.parse(form.value)
    return true
  } catch (error) {
    if (error instanceof z.ZodError) {
      const firstError = error.errors[0]
      eventBus.emit('showToast', { message: firstError.message, type: 'warning' })
    }
    return false
  }
}

const handleSubmit = async () => {
  if (!validateForm()) return
  try {
    isLoading.value = true
    await apiFetch('/api/auth/register', {
      method: 'POST',
      body: {
        email: form.value.email,
        code: form.value.code,
        password: form.value.password,
      },
    })
    eventBus.emit('showToast', { message: '注册成功！', type: 'success' })
    router.push('/login')
  } catch (error) {
  } finally {
    isLoading.value = false
  }
}
</script>
