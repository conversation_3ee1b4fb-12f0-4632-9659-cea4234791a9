<template>
  <div class="min-h-screen bg-gray-50 py-12 px-4 sm:px-6 lg:px-8">
    <div class="max-w-4xl mx-auto">
      <div class="bg-white shadow rounded-lg p-6">
        <h1 class="text-2xl font-bold text-gray-900 mb-6">批量注册内测用户</h1>
        
        <form @submit.prevent="handleSubmit" class="space-y-6">
          <!-- 管理员密钥 -->
          <div>
            <label for="adminKey" class="block text-sm font-medium text-gray-700">
              管理员密钥
            </label>
            <input
              id="adminKey"
              v-model="form.adminKey"
              type="password"
              required
              class="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500"
              placeholder="请输入管理员密钥"
            />
          </div>

          <!-- 邮箱列表 -->
          <div>
            <label for="emails" class="block text-sm font-medium text-gray-700">
              邮箱列表 (每行一个邮箱，最多100个)
            </label>
            <textarea
              id="emails"
              v-model="form.emailsText"
              rows="10"
              required
              class="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500"
              placeholder="<EMAIL>&#10;<EMAIL>&#10;<EMAIL>"
            ></textarea>
            <p class="mt-2 text-sm text-gray-500">
              当前邮箱数量: {{ emailCount }}
            </p>
          </div>

          <!-- 提交按钮 -->
          <div>
            <button
              type="submit"
              :disabled="isLoading || emailCount === 0"
              class="w-full flex justify-center py-2 px-4 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 disabled:opacity-50 disabled:cursor-not-allowed"
            >
              <span v-if="isLoading" class="flex items-center">
                <svg class="animate-spin -ml-1 mr-3 h-5 w-5 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                  <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                  <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                </svg>
                处理中...
              </span>
              <span v-else>批量注册用户</span>
            </button>
          </div>
        </form>

        <!-- 结果显示 -->
        <div v-if="result" class="mt-8">
          <h2 class="text-lg font-medium text-gray-900 mb-4">注册结果</h2>
          
          <!-- 汇总信息 -->
          <div class="bg-gray-50 rounded-lg p-4 mb-4">
            <div class="grid grid-cols-3 gap-4 text-center">
              <div>
                <div class="text-2xl font-bold text-blue-600">{{ result.summary.total }}</div>
                <div class="text-sm text-gray-500">总计</div>
              </div>
              <div>
                <div class="text-2xl font-bold text-green-600">{{ result.summary.success }}</div>
                <div class="text-sm text-gray-500">成功</div>
              </div>
              <div>
                <div class="text-2xl font-bold text-red-600">{{ result.summary.failure }}</div>
                <div class="text-sm text-gray-500">失败</div>
              </div>
            </div>
          </div>

          <!-- 详细结果 -->
          <div class="space-y-2 max-h-96 overflow-y-auto">
            <div
              v-for="(item, index) in result.results"
              :key="index"
              class="flex items-center justify-between p-3 rounded-lg"
              :class="item.success ? 'bg-green-50 border border-green-200' : 'bg-red-50 border border-red-200'"
            >
              <div class="flex-1">
                <div class="font-medium" :class="item.success ? 'text-green-900' : 'text-red-900'">
                  {{ item.email }}
                </div>
                <div v-if="item.success" class="text-sm text-green-700">
                  密码: <span class="font-mono bg-green-100 px-2 py-1 rounded">{{ item.password }}</span>
                </div>
                <div v-else class="text-sm text-red-700">
                  错误: {{ item.error }}
                </div>
              </div>
              <div class="ml-4">
                <span
                  class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium"
                  :class="item.success ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'"
                >
                  {{ item.success ? '成功' : '失败' }}
                </span>
              </div>
            </div>
          </div>

          <!-- 导出按钮 -->
          <div class="mt-4">
            <button
              @click="exportResults"
              class="inline-flex items-center px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500"
            >
              <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
              </svg>
              导出结果
            </button>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue'
import { apiFetch } from '~/utils/api'

definePageMeta({
  layout: false
})

const form = ref({
  adminKey: '',
  emailsText: ''
})

const isLoading = ref(false)
const result = ref<any>(null)

const emailCount = computed(() => {
  if (!form.value.emailsText.trim()) return 0
  return form.value.emailsText
    .split('\n')
    .map(email => email.trim())
    .filter(email => email.length > 0).length
})

const handleSubmit = async () => {
  if (!form.value.adminKey.trim()) {
    alert('请输入管理员密钥')
    return
  }

  if (!form.value.emailsText.trim()) {
    alert('请输入邮箱列表')
    return
  }

  const emails = form.value.emailsText
    .split('\n')
    .map(email => email.trim())
    .filter(email => email.length > 0)

  if (emails.length === 0) {
    alert('请输入有效的邮箱地址')
    return
  }

  if (emails.length > 100) {
    alert('一次最多只能注册100个用户')
    return
  }

  try {
    isLoading.value = true
    result.value = null

    const response = await apiFetch('/api/admin/batch-register', {
      method: 'POST',
      body: {
        emails,
        adminKey: form.value.adminKey
      }
    })

    result.value = response
  } catch (error: any) {
    console.error('批量注册失败:', error)
    alert('批量注册失败: ' + (error.message || '未知错误'))
  } finally {
    isLoading.value = false
  }
}

const exportResults = () => {
  if (!result.value) return

  const successResults = result.value.results.filter((item: any) => item.success)
  
  if (successResults.length === 0) {
    alert('没有成功的注册结果可以导出')
    return
  }

  const csvContent = [
    '邮箱,密码,用户ID',
    ...successResults.map((item: any) => `${item.email},${item.password},${item.userId}`)
  ].join('\n')

  const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' })
  const link = document.createElement('a')
  const url = URL.createObjectURL(blob)
  link.setAttribute('href', url)
  link.setAttribute('download', `batch-register-results-${new Date().toISOString().split('T')[0]}.csv`)
  link.style.visibility = 'hidden'
  document.body.appendChild(link)
  link.click()
  document.body.removeChild(link)
}
</script>
