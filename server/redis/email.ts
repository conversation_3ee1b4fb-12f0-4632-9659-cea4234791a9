// import { ddb, getAll, getOne } from '~/server/ddb'
import { redis } from '~/server/redis'
import { config } from '../config'
import { getCreatedTime } from '../utils/date'

export const $PKMailCodeId = (id: string) => `Mail#${id}`
export const $SKMailAddress = (mailAddress: string) => `Mail:Address#${mailAddress}`
export const $DataMailCode = (code: string) => `Mail:Code#${code}`
export const $RedisMailRecord = (mailAddress: string) => `${$SKMailAddress(mailAddress)}`
export const $RedisMailSendLock = (mailAddress: string) => `${$SKMailAddress(mailAddress)}+LOCK`

interface MailCodeRecord {
  Address: string
  Code: string
  CreateAt: string
}

export async function addMailCodeRecord(mailAddr: string, code: string) {
  const locked = await redis.exists($RedisMailSendLock(mailAddr))
  if (locked) {
    return false
  }
  const recordKey = $RedisMailRecord(mailAddr)
  const lockKey = $RedisMailSendLock(mailAddr)
  await redis.set(lockKey, '1', { EX: config.resend.mailSendLockTime, NX: true })
  await redis.setJSON(recordKey, { Address: mailAddr, Code: code, CreateAt: getCreatedTime().createdAt } as MailCodeRecord, {
    EX: config.resend.mailExpireTime,
  })
  return true
}

export async function isLocked(mailAddr: string) {
  return redis.exists($RedisMailSendLock(mailAddr))
}

export async function getMailCodeRecord(mailAddr: string, code: string) {
  logger.info('get mail record => ', mailAddr, code)
  const res = await redis.getJSON<MailCodeRecord>($RedisMailRecord(mailAddr))
  if (res?.Code === code) {
    return res
  }
}

export function removeMailCodeRecord(mailAddr: string) {
  return redis.del($RedisMailRecord(mailAddr))
}
