import { H3Event } from 'h3'
import { v4 as uuidv4 } from 'uuid'
import { z } from 'zod'
import { userWallet } from '~/server/daos/user-wallet'
import { User } from '~/server/models/user.model'
import { Wallet } from '~/server/models/wallet.model'
import { generateRandomName, generateUsernameFromEmail } from '~/server/services/auth.service'
import { defineRouteHandler } from '~/server/utils/handler'
import logger from '~/server/utils/logger'
import { respError, respSuccess } from '~/server/utils/response'
import { ErrorCode } from '~/utils/error-code'

// 生成随机密码
const generateRandomPassword = (length: number = 8): string => {
  const charset = 'abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789'
  let password = ''
  for (let i = 0; i < length; i++) {
    password += charset.charAt(Math.floor(Math.random() * charset.length))
  }
  return password
}

// 验证邮箱格式
const isValidEmail = (email: string): boolean => {
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/
  return emailRegex.test(email)
}

const batchRegisterSchema = z.object({
  emails: z.array(z.string().email('无效的邮箱地址')).min(1, '至少需要一个邮箱').max(100, '一次最多注册100个用户'),
  adminKey: z.string().min(1, '管理员密钥不能为空'),
})

export default defineRouteHandler({ auth: false, bodyRules: batchRegisterSchema }, async (event: H3Event, { body }) => {
  const { emails, adminKey } = body

  // 简单的管理员验证（在生产环境中应该使用更安全的方式）
  const ADMIN_KEY = process.env.ADMIN_BATCH_REGISTER_KEY || 'your-secret-admin-key'
  if (adminKey !== ADMIN_KEY) {
    logger.warn('批量注册尝试使用了错误的管理员密钥', {
      ip: getRequestIP(event),
      timestamp: new Date().toISOString(),
    })
    return respError(ErrorCode.UNAUTHORIZED.code, '管理员密钥错误')
  }

  try {
    const results: Array<{
      email: string
      password: string
      success: boolean
      error?: string
      userId?: string
    }> = []

    // 去重邮箱
    const uniqueEmails = [...new Set(emails.map(email => email.toLowerCase().trim()))]

    logger.info('开始批量注册内测用户', {
      totalEmails: uniqueEmails.length,
      adminIp: getRequestIP(event),
    })

    for (const email of uniqueEmails) {
      try {
        // 验证邮箱格式
        if (!isValidEmail(email)) {
          results.push({
            email,
            password: '',
            success: false,
            error: '邮箱格式无效',
          })
          continue
        }

        // 检查用户是否已存在
        const existingUser = await User.findOne({ email })
        if (existingUser) {
          results.push({
            email,
            password: '',
            success: false,
            error: '用户已存在',
          })
          continue
        }

        // 生成用户数据
        const userId = uuidv4()
        const password = generateRandomPassword(10)
        const username = generateUsernameFromEmail(email)
        const name = generateRandomName()
        const avatarUrl = `https://api.dicebear.com/7.x/avataaars/svg?seed=${crypto.randomUUID()}`

        // 创建用户
        const user = await User.create({
          _id: userId,
          email: email.toLowerCase(),
          password: password,
          username: username,
          name: name,
          image: avatarUrl,
        })

        // 创建钱包（初始为0 tokens）
        await Wallet.create({
          userId: user._id,
          planKey: 'free',
          paymentType: 'free',
          activated: true,
          remainingTokens: 0,
        })

        // 使用 userWallet 服务添加 100 credit
        await userWallet.addTokenByPlanKey(user._id, 'free')

        results.push({
          email,
          password,
          success: true,
          userId: user._id,
        })

        logger.info('内测用户创建成功', {
          email,
          userId: user._id,
          username: user.username,
        })
      } catch (error: any) {
        logger.error('创建内测用户失败', {
          email,
          error: error.message,
          stack: error.stack,
        })

        results.push({
          email,
          password: '',
          success: false,
          error: error.message || '创建用户失败',
        })
      }
    }

    const successCount = results.filter(r => r.success).length
    const failureCount = results.length - successCount

    logger.info('批量注册内测用户完成', {
      total: results.length,
      success: successCount,
      failure: failureCount,
    })

    return respSuccess({
      message: `批量注册完成，成功: ${successCount}，失败: ${failureCount}`,
      results: results,
      summary: {
        total: results.length,
        success: successCount,
        failure: failureCount,
      },
    })
  } catch (error: any) {
    logger.error('批量注册内测用户过程中发生错误', {
      error: error.message,
      stack: error.stack,
      adminIp: getRequestIP(event),
    })

    return respError(ErrorCode.INTERNAL_SERVER_ERROR.code, '批量注册过程中发生错误: ' + error.message)
  }
})
