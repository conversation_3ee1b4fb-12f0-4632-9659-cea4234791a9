import { H3Event, sendStream } from 'h3'
import { z } from 'zod'
import { LLM } from '~/server/ai'
import { generateQuestionsPrompt } from '~/server/prompt-service/generate-questions'
import { patchModel } from '~/server/utils/LLM'
import { handleApiError } from '~/server/utils/error-handler'
import { ErrorCode } from '~/utils/error-code'

const modelSchema = ['standard', 'professional'] as const
const languageSchema = ['zh-CN', 'zh-TW', 'en'] as const
const roleSchema = ['hr', 'profession'] as const
const typeSchema = ['pureProblem', 'questionWithAnswer'] as const

const schema = z.object({
  text: z.string(),
  role: z.enum(roleSchema).default('profession'), // 面试官角色：hr还是专业面试官
  model: z.enum(modelSchema).default('standard'), // 优化模式：标准优化还是专业优化
  language: z.enum(languageSchema).default('zh-CN'), //输出语言
  jobInfo: z.string().optional(), // JD信息
  questionCountObj: z.object({
    questionGroupCount: z.string().default('10'),
    questionPerGroup: z.string().default('4'),
  }), // 问题数量
  predictionType: z.enum(typeSchema).default('pureProblem'), // 预测类型：纯问题或带回答
})

interface PromptOptions {
  language: (typeof languageSchema)[number]
  jobInfo?: string
}

export default defineRouteHandler({ auth: true, bodyRules: schema }, async (event: H3Event, { session, body }) => {
  const { text, role, model, language, jobInfo = '', questionCountObj, predictionType } = body
  const userId = session?.user?.id

  try {
    const llmModel = patchModel(model)
    const finalPrompt = generateQuestionsPrompt(role, text, jobInfo, questionCountObj, predictionType)
    const llm = await LLM.create(
      llmModel,
      [
        {
          role: 'user',
          content: finalPrompt,
        },
      ],
      userId,
    )
    const stream = await llm.usePrompt().withCreditCheck().useModel()
    if (!stream) {
      throw new Error('模型调用失败')
    }
    const rs = stream.toReadableStream()
    return sendStream(event, rs)
  } catch (error) {
    console.error('聊天接口调用失败:', error)
    return handleApiError(event, error, ErrorCode.INTERNAL_SERVER_ERROR)
  }
})
