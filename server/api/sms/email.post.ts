import { H3Event } from 'h3'
import { z } from 'zod'
import { config } from '~/server/config'
import { addMailCodeRecord, isLocked } from '~/server/redis/email'
import { Resend } from 'resend'
import { ErrorCode } from '~/utils/error-code'

const schema = z.object({
  email: z.string().email('Invalid email format'),
})

// 生成6位数字验证码
const generateVerificationCode = () => {
  return Math.floor(100000 + Math.random() * 900000).toString()
}

export default defineRouteHandler({ auth: false, bodyRules: schema }, async (event: H3Event, { body }) => {
  const { email } = body
  try {
    // 首先检查是否被锁定（防止频繁发送）
    const locked = await isLocked(email)
    if (locked) {
      return respError(ErrorCode.TOO_MANY_REQUESTS.code, ErrorCode.TOO_MANY_REQUESTS.displayMessage)
    }
    
    const verificationCode = generateVerificationCode()
    logger.info(`Verification code: ${verificationCode}`)
    
    // 初始化 Resend 客户端
    const resend = new Resend(config.resend.apiKey)
    
    // 发送邮件
    const emailData = await resend.emails.send({
      from: `${config.resend.fromName} <${config.resend.fromAddress}>`,
      to: [email],
      subject: 'Hi-Offer - 验证码',
      html: `
        <div style="font-family: Arial, sans-serif; padding: 20px;">
          <h2>您的验证码</h2>
          <p>您的验证码是：<strong style="font-size: 24px;">${verificationCode}</strong></p>
          <p>验证码有效期为 5 分钟，请勿将验证码泄露给他人。</p>
        </div>
      `,
    })
    
    // 将验证码存储到缓存中
    await addMailCodeRecord(email, verificationCode)
    
    logger.info('Email sent successfully:', emailData)
    
    return respSuccess({
      message: 'Verification code sent',
    })
  } catch (error) {
    logger.error('Send verification code error:', error)
    return respError(ErrorCode.INTERNAL_SERVER_ERROR.code, ErrorCode.INTERNAL_SERVER_ERROR.displayMessage)
  }
})
