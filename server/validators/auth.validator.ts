import { z } from 'zod'

export const registerSchema = z.object({
  email: z.string().email('无效的邮箱地址'),
  code: z.string().length(6, '验证码必须是6位数字'),
  password: z.string().min(6, '密码至少6个字符'),
})

export const loginSchema = z.object({
  email: z.string().email('无效的邮箱地址'),
  password: z.string().min(6, '密码至少6个字符'),
})

export type RegisterInput = z.infer<typeof registerSchema>
export type LoginInput = z.infer<typeof loginSchema>
