import mongoose from 'mongoose'

const walletSchema = new mongoose.Schema(
  {
    userId: {
      type: String,
      required: true,
      index: true,
    },

    activated: {
      // 是否激活
      type: Boolean,
      default: false,
    },
    createdAt: {
      // 创建时间
      type: Date,
      default: Date.now,
    },
    totalUsedTokens: {
      // token 使用次数
      type: Number,
      default: 0,
    },
    remainingTokens: {
      // token 剩余次数
      type: Number,
      required: true,
    },
  },
  {
    timestamps: true,
    versionKey: false,
  },
)

export type WalletType = mongoose.InferSchemaType<typeof walletSchema>
export const Wallet = mongoose.model<WalletType>('Wallet', walletSchema)
