import mongoose from 'mongoose'

const walletSchema = new mongoose.Schema(
  {
    userId: {
      type: String,
      required: true,
      index: true,
    },
    // 该字段废弃
    paymentType: {
      // 支付方式 (已废弃，保留用于兼容旧数据)
      type: String,
      required: false,
    },
    // 该字段废弃
    planKey: {
      // 套餐类型 (已废弃，保留用于兼容旧数据)
      type: String,
      required: false,
    },
    activated: {
      // 是否激活
      type: Boolean,
      default: false,
    },
    createdAt: {
      // 创建时间
      type: Date,
      default: Date.now,
    },
    totalUsedTokens: {
      // token 使用次数
      type: Number,
      default: 0,
    },
    remainingTokens: {
      // token 剩余次数
      type: Number,
      required: true,
    },
  },
  {
    timestamps: true,
    versionKey: false,
  },
)

export type WalletType = mongoose.InferSchemaType<typeof walletSchema>
export const Wallet = mongoose.model<WalletType>('Wallet', walletSchema)
