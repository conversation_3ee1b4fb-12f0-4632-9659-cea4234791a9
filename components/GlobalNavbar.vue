<script setup lang="ts">
import { useRoute } from 'vue-router'
import { Cog6ToothIcon, ArrowRightOnRectangleIcon, CircleStackIcon } from '@heroicons/vue/24/outline'
import { useUserStore } from '~/stores/user'
import { useCredit } from '~/composables/useCredit'
import { useEventBus } from '~/composables/useEventBus'
import { onClickOutside } from '@vueuse/core'

import Logo from '@/assets/icons/logo.svg'
import CreditsIcon from '@/assets/icons/credits.svg'
import ExitIcon from '@/assets/icons/exit.svg'
import SettingIcon from '@/assets/icons/setting.svg'
const { signOut, data: session } = useAuth()
const userStore = useUserStore()
const { remainingCredits } = useCredit()
const eventBus = useEventBus()

const route = useRoute()

// 定义属性
const props = defineProps({
  shouldShowSidebar: {
    type: Boolean,
    default: true,
  },
})

// 用户下拉菜单状态
const showUserDropdown = ref(false)
const userDropdownRef = ref(null)
const userAvatarRef = ref<HTMLElement | null>(null)

// 点击外部关闭下拉菜单，但排除头像和积分盒子区域
onClickOutside(
  userDropdownRef,
  () => {
    showUserDropdown.value = false
  },
  { ignore: [userAvatarRef] },
)

// 切换用户下拉菜单显示状态
const toggleUserDropdown = () => {
  showUserDropdown.value = !showUserDropdown.value
}

// 退出登录
const handleLogout = async () => {
  try {
    await signOut({ callbackUrl: '/' })
  } catch (error) {
    console.error('退出登录失败:', error)
    eventBus.emit('showToast', { message: '退出登录失败', type: 'error' })
  }
}

const userInfo = computed(() => ({
  name: userStore.name || '未设置姓名',
  image: userStore.image,
  bio: userStore.bio,
}))
</script>

<template>
  <nav class="fixed top-0 z-40 flex h-14 w-full items-center justify-between border-b border-gray-200 bg-white px-4 shadow-sm transition-all duration-300">
    <!-- 左侧品牌和导航 -->
    <div class="flex items-center overflow-hidden">
      <!-- 侧边栏开关按钮 -->
      <div class="transition-all duration-300" :class="{ 'opacity-0': !shouldShowSidebar && !$slots['sidebar-toggle'] }">
        <slot name="sidebar-toggle"></slot>
      </div>

      <!-- Logo 和品牌名 -->
      <NuxtLink to="/dashboard" class="flex items-center">
        <Logo class="h-8 w-8 text-indigo-600" />
        <span class="ml-2 bg-gradient-to-r from-purple-400 to-indigo-600 bg-clip-text text-xl font-semibold text-transparent">Hi-Offer</span>
      </NuxtLink>
    </div>

    <!-- 右侧工具和用户信息 -->
    <div class="flex items-center space-x-3">
      <!-- 积分按钮 -->
      <button
        @click="eventBus.emit('showPaywall')"
        class="flex items-center rounded-lg bg-gradient-to-r from-indigo-500 to-purple-600 px-3 py-1.5 text-sm font-medium text-white shadow-sm transition-all hover:opacity-90"
      >
        <span class="i-carbon-money mr-1.5 text-white"></span>
        购买 Credits
        <!-- <span class="ml-1 font-bold">{{ remainingCredits }}</span> -->
      </button>

      <!-- 升级按钮 -->
      <!-- <NuxtLink
        to="/settings/subscription"
        class="flex items-center rounded-lg bg-indigo-600 px-3 py-1.5 text-sm font-medium text-white shadow-sm transition-all hover:bg-indigo-700"
      >
        升级
      </NuxtLink> -->

      <div class="relative flex items-center space-x-2">
        <!-- 用户头像和积分 -->
        <div class="flex items-center space-x-2" ref="userAvatarRef" @click="toggleUserDropdown">
          <!-- 积分显示 -->
          <div class="flex items-center rounded-full bg-purple-100 px-2 py-1 pr-4">
            <img
              :src="userStore.image || 'https://api.dicebear.com/7.x/notionists/svg?seed=John'"
              class="h-7 w-7 rounded-full object-cover ring-2 ring-white"
              alt="用户头像"
            />
            <div class="absolute -bottom-0.5 -right-0.5 h-3 w-3 rounded-full bg-green-400 ring-1 ring-white"></div>

            <div class="ml-1.5 flex items-center">
              <CircleStackIcon class="size-5 shrink-0 text-gray-600 transition-colors duration-200" />
              <span class="ml-0.5 text-sm font-medium text-gray-700">{{ remainingCredits }}</span>
            </div>
          </div>
        </div>

        <!-- 用户下拉菜单 -->
        <Transition
          name="dropdown"
          enter-active-class="transition ease-out duration-300"
          enter-from-class="transform opacity-0 scale-95"
          enter-to-class="transform opacity-100 scale-100"
          leave-active-class="transition ease-in duration-300"
          leave-from-class="transform opacity-100 scale-100"
          leave-to-class="transform opacity-0 scale-95"
        >
          <div
            v-show="showUserDropdown"
            id="user-dropdown"
            ref="userDropdownRef"
            class="absolute right-0 top-full z-50 mt-2 w-72 rounded-lg bg-white p-4 shadow-lg ring-1 ring-gray-200"
          >
            <div class="mb-3 flex items-center space-x-3">
              <img :src="userStore.image" class="h-9 w-9 rounded-full object-cover ring-2 ring-white" alt="用户头像" />
              <div class="flex flex-col">
                <span class="font-medium text-gray-800">{{ userInfo.name }}</span>
                <!-- <span class="text-sm text-gray-500">{{ userInfo.email }}</span> -->
              </div>
            </div>

            <div class="mb-3 flex items-center justify-between">
              <span class="text-sm text-gray-600">试用</span>
              <span class="text-sm text-blue-600">剩余 {{ remainingCredits }}</span>
            </div>

            <div class="mb-3 h-1 w-full rounded-full bg-blue-500"></div>
            <!-- <button
              @click="eventBus.emit('showPaywall')"
              class="flex w-full items-center justify-center rounded-lg rounded-md bg-gradient-to-r from-indigo-500 to-purple-600 px-4 py-2 text-base font-medium text-white shadow-sm transition-all hover:opacity-90"
            >
              购买更多 Credits!
            </button> -->
            <div class="mt-2 space-y-4">
              <NuxtLink
                to="/settings"
                :class="[
                  route.path === '/settings'
                    ? 'border-l-4 border-indigo-600 bg-indigo-50 text-indigo-700 shadow-sm'
                    : 'text-gray-700 hover:bg-gray-50/80 hover:text-indigo-600',
                  'group flex items-center gap-x-3 rounded-md p-3 text-sm font-medium transition-all duration-200 ease-in-out',
                ]"
              >
                <Cog6ToothIcon
                  :class="[
                    route.path === '/settings' ? 'text-indigo-700' : 'text-gray-400 group-hover:text-indigo-600',
                    'size-6 shrink-0 transition-colors duration-200',
                  ]"
                  aria-hidden="true"
                />
                设置
              </NuxtLink>

              <!-- <NuxtLink to="/shortcuts" class="flex items-center text-gray-700 hover:text-gray-900">
                <span class="i-carbon-keyboard mr-3 text-gray-500"></span>
                <span>快捷键</span>
              </NuxtLink>

              <NuxtLink to="/feedback" class="flex items-center text-gray-700 hover:text-gray-900">
                <span class="i-carbon-email mr-3 text-gray-500"></span>
                <span>反馈</span>
              </NuxtLink>

              <NuxtLink to="/faq" class="flex items-center text-gray-700 hover:text-gray-900">
                <span class="i-carbon-help mr-3 text-gray-500"></span>
                <span>常见问题</span>
              </NuxtLink> -->

              <!-- <div class="flex items-center justify-between border-t pt-3">
                 <div class="flex items-center text-gray-700 hover:text-gray-900">
                   <span class="i-carbon-plugin mr-3 text-gray-500"></span>
                   <span>插件</span>
                 </div>
                 <span class="text-xs text-blue-600">安装并获得 30 Credits</span>
               </div> -->
            </div>

            <button
              @click="handleLogout"
              class="group flex w-full items-center gap-x-3 rounded-md p-3 text-sm font-medium text-gray-700 transition-all duration-200 ease-in-out hover:bg-gray-50/80 hover:text-gray-900"
            >
              <ArrowRightOnRectangleIcon class="size-6 shrink-0 text-gray-400 transition-colors duration-200 group-hover:text-gray-600" aria-hidden="true" />
              退出登录
            </button>
          </div>
        </Transition>
      </div>
    </div>
  </nav>

  <!-- 占位空间，防止内容被导航栏遮挡 -->
  <div class="h-14"></div>
</template>

<style scoped>
/* 下拉菜单过渡效果 */
.dropdown-enter-active,
.dropdown-leave-active {
  transition: all 0.3s ease;
  transform-origin: top right;
}
</style>
