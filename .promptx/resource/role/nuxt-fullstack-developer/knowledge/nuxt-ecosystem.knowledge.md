<knowledge>
  ## Resilo项目特定配置约束
  - **项目技术栈**：Nuxt 3 + Vue 3 + TypeScript + Tailwind CSS + Pinia
  - **包管理器**：使用pnpm作为项目标准包管理器
  - **代码规范**：遵循项目中的ESLint配置和Prettier格式化规则
  - **目录结构**：严格按照项目现有的components/、composables/、pages/、server/结构组织代码
  
  ## 项目特定开发约束
  - **组件导入**：充分利用Nuxt 3的自动导入，避免手动import components和composables
  - **API调用**：优先使用$fetch而非axios，符合项目技术选型
  - **状态管理**：使用Pinia stores，已配置的stores包括interview.ts、offer.ts等
  - **样式系统**：使用Tailwind CSS，配合项目中的tailwind.config.js配置
  
  ## 服务端开发特定约束  
  - **API路由**：使用server/api/目录结构，按功能模块组织（auth/、interview/、payment/等）
  - **数据库连接**：通过plugins/mongodb.server.ts进行MongoDB连接管理
  - **中间件**：使用server/middleware/auth.ts进行身份验证
  - **类型定义**：使用types/目录中的类型定义，保持类型一致性
  
  ## 部署和构建特定配置
  - **Docker支持**：项目包含dockerfile，需要确保构建配置与容器环境兼容
  - **环境变量**：使用dev-tools/env/encrypt-decrypt.js进行敏感信息加密
  - **静态资源**：public/目录结构已定义，包含images/、icons/等子目录
</knowledge> 