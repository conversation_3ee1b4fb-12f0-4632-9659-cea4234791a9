<execution>
  <constraint>
    ## Nuxt 3技术约束
    - **Node.js版本要求**：必须使用Node.js 16.10+或18+版本
    - **包管理器约束**：优先使用pnpm，其次yarn，避免npm的扁平化问题
    - **TypeScript严格模式**：开启strict: true，确保类型安全
    - **构建目标约束**：必须支持现代浏览器，ES2020+目标
    - **服务端渲染限制**：避免在服务端使用浏览器专有API
    - **Hydration约束**：确保服务端和客户端渲染结果一致性
  </constraint>

  <rule>
    ## 强制性开发规则
    - **目录结构规范**：严格遵循Nuxt 3约定，禁止自定义核心目录名称
    - **组件命名规范**：PascalCase组件名，kebab-case文件名
    - **Composables命名**：使用use前缀，camelCase命名
    - **API路由规范**：使用RESTful约定，正确的HTTP动词
    - **类型定义强制**：所有公共API必须有TypeScript类型定义
    - **SSR兼容性**：所有代码必须同时支持服务端和客户端运行
  </rule>

  <guideline>
    ## 开发指导原则
    - **性能优先**：优先考虑Core Web Vitals指标优化
    - **用户体验导向**：以用户交互体验为第一优先级
    - **渐进式增强**：基础功能优先，高级特性渐进式添加
    - **移动优先设计**：Tailwind CSS移动优先响应式设计
    - **可访问性重视**：遵循WCAG 2.1 AA标准
    - **SEO友好**：合理使用meta标签、结构化数据、语义化HTML
  </guideline>

  <process>
    ## Nuxt开发标准流程
    
    ### Step 1: 项目初始化
    ```mermaid
    flowchart TD
        A[npx nuxi@latest init] --> B[配置nuxt.config.ts]
        B --> C[安装依赖包]
        C --> D[配置TypeScript]
        D --> E[设置ESLint/Prettier]
        E --> F[配置Tailwind CSS]
        F --> G[设置开发环境]
    ```
    
    ### Step 2: 架构搭建
    ```mermaid
    graph LR
        A[页面路由设计] --> B[组件库搭建]
        B --> C[状态管理配置]
        C --> D[API接口设计]
        D --> E[中间件配置]
        E --> F[插件系统]
    ```
    
    ### Step 3: 开发迭代流程
    ```mermaid
    flowchart TD
        A[需求分析] --> B[API设计]
        B --> C[数据模型定义]
        C --> D[后端接口开发]
        D --> E[前端页面开发]
        E --> F[组件封装]
        F --> G[状态集成]
        G --> H[样式优化]
        H --> I[性能测试]
        I --> J{是否达标?}
        J -->|否| K[性能优化]
        K --> I
        J -->|是| L[功能测试]
        L --> M[代码审查]
        M --> N[部署发布]
    ```
    
    ### Step 4: 质量保证流程
    
    #### 代码质量检查
    ```bash
    # TypeScript类型检查
    npx nuxi typecheck
    
    # ESLint代码规范
    npm run lint
    
    # 性能分析
    npm run analyze
    
    # 构建测试
    npm run build
    ```
    
    #### 性能监控流程
    ```mermaid
    graph TD
        A[Lighthouse审计] --> B[Core Web Vitals检查]
        B --> C[Bundle分析]
        C --> D[渲染性能测试]
        D --> E[网络性能测试]
        E --> F[移动端测试]
        F --> G[性能报告]
    ```
  </process>

  <criteria>
    ## 质量评价标准
    
    ### 性能指标
    - ✅ **Lighthouse Score** ≥ 90分（Performance）
    - ✅ **LCP** ≤ 2.5秒（最大内容绘制）
    - ✅ **FID** ≤ 100ms（首次输入延迟）
    - ✅ **CLS** ≤ 0.1（累积布局偏移）
    - ✅ **TTFB** ≤ 800ms（首字节时间）
    
    ### 代码质量
    - ✅ **TypeScript覆盖率** ≥ 95%
    - ✅ **ESLint检查** 零错误零警告
    - ✅ **组件复用率** ≥ 70%
    - ✅ **API响应时间** ≤ 200ms
    - ✅ **构建体积** 首屏资源 ≤ 300KB
    
    ### 用户体验
    - ✅ **移动端适配** 100%响应式
    - ✅ **无障碍访问** WCAG 2.1 AA合规
    - ✅ **浏览器兼容** 支持现代浏览器99%+
    - ✅ **SEO友好** Meta标签完整、语义化HTML
    - ✅ **错误处理** 优雅的错误页面和提示
    
    ### 开发效率
    - ✅ **开发服务器启动** ≤ 3秒
    - ✅ **热更新响应** ≤ 1秒
    - ✅ **构建时间** ≤ 2分钟
    - ✅ **部署时间** ≤ 5分钟
    - ✅ **代码审查效率** 平均 ≤ 30分钟
  </criteria>
</execution> 