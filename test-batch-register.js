#!/usr/bin/env node

/**
 * 批量注册内测用户接口测试脚本
 * 使用方法: node test-batch-register.js
 */

const testBatchRegister = async () => {
  const testEmails = ['<EMAIL>', '<EMAIL>', '<EMAIL>']

  const testData = {
    emails: testEmails,
    adminKey: process.env.ADMIN_BATCH_REGISTER_KEY || 'your-secret-admin-key', // 从环境变量读取或使用默认值
  }

  try {
    console.log('🚀 开始测试批量注册接口...')
    console.log('📧 测试邮箱:', testEmails)

    const response = await fetch('http://localhost:3000/api/admin/batch-register', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(testData),
    })

    const result = await response.json()

    if (response.ok) {
      console.log('✅ 接口调用成功!')
      console.log('📊 注册结果汇总:')
      console.log(`   总计: ${result.data.summary.total}`)
      console.log(`   成功: ${result.data.summary.success}`)
      console.log(`   失败: ${result.data.summary.failure}`)

      console.log('\n📋 详细结果:')
      result.data.results.forEach((item, index) => {
        if (item.success) {
          console.log(`   ${index + 1}. ✅ ${item.email}`)
          console.log(`      密码: ${item.password}`)
          console.log(`      用户ID: ${item.userId}`)
        } else {
          console.log(`   ${index + 1}. ❌ ${item.email}`)
          console.log(`      错误: ${item.error}`)
        }
      })
    } else {
      console.log('❌ 接口调用失败!')
      console.log('错误信息:', result)
    }
  } catch (error) {
    console.error('❌ 测试过程中发生错误:', error.message)
  }
}

// 运行测试
testBatchRegister()
